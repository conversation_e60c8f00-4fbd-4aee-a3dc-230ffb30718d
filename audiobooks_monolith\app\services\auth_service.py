"""
用户认证服务
提供用户注册、登录、JWT令牌管理等功能
"""

import bcrypt
from jose import jwt
from datetime import datetime, timedelta
from typing import Optional
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.logging import get_logger
from app.core.exceptions import (
    AuthenticationError,
    UserNotFoundError,
    InvalidTokenError,
    TokenExpiredError,
    ValidationError
)
from app.models.user import User, UserCreate, UserLogin, TokenData
from app.core.database import get_db

logger = get_logger(__name__)


class AuthService:
    """用户认证服务"""

    def __init__(self):
        self.secret_key = settings.secret_key
        self.algorithm = settings.jwt_algorithm
        self.expire_minutes = settings.jwt_expire_minutes

    def hash_password(self, password: str) -> str:
        """密码哈希"""
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        try:
            return bcrypt.checkpw(
                plain_password.encode('utf-8'),
                hashed_password.encode('utf-8')
            )
        except Exception as e:
            logger.error(f"密码验证失败: {str(e)}")
            return False

    def create_access_token(self, user_id: int, email: str) -> tuple[str, datetime]:
        """创建访问令牌"""
        expire = datetime.utcnow() + timedelta(minutes=self.expire_minutes)
        
        payload = {
            "user_id": user_id,
            "email": email,
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access"
        }
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        return token, expire

    def verify_token(self, token: str) -> TokenData:
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            user_id = payload.get("user_id")
            email = payload.get("email")
            
            if user_id is None or email is None:
                raise InvalidTokenError("令牌数据不完整")
                
            return TokenData(user_id=user_id, email=email)
            
        except jwt.ExpiredSignatureError:
            raise TokenExpiredError("令牌已过期")
        except jwt.JWTError as e:
            raise InvalidTokenError(f"无效的令牌: {str(e)}")

    def get_user_by_email(self, db: Session, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        try:
            return db.query(User).filter(User.email == email).first()
        except Exception as e:
            logger.error(f"查询用户失败: {str(e)}")
            return None

    def get_user_by_id(self, db: Session, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        try:
            return db.query(User).filter(User.id == user_id).first()
        except Exception as e:
            logger.error(f"查询用户失败: {str(e)}")
            return None

    def create_user(self, db: Session, user_create: UserCreate) -> User:
        """创建新用户"""
        try:
            # 检查邮箱是否已存在
            existing_user = self.get_user_by_email(db, user_create.email)
            if existing_user:
                raise ValidationError("邮箱已被注册")

            # 检查用户数量限制
            user_count = db.query(User).count()
            if user_count >= settings.max_users:
                raise ValidationError("用户数量已达上限")

            # 创建用户
            hashed_password = self.hash_password(user_create.password)
            
            db_user = User(
                email=user_create.email,
                hashed_password=hashed_password,
                points=settings.default_user_points,
                is_active=True,
                is_verified=False
            )
            
            db.add(db_user)
            db.commit()
            db.refresh(db_user)
            
            logger.info(f"用户创建成功: {user_create.email}")
            return db_user
            
        except Exception as e:
            db.rollback()
            logger.error(f"创建用户失败: {str(e)}")
            raise

    def authenticate_user(self, db: Session, user_login: UserLogin) -> User:
        """用户认证"""
        try:
            # 获取用户
            user = self.get_user_by_email(db, user_login.email)
            if not user:
                raise AuthenticationError("邮箱或密码错误")

            # 检查用户状态
            if not user.is_active:
                raise AuthenticationError("用户账户已被禁用")

            # 验证密码
            if not self.verify_password(user_login.password, user.hashed_password):
                raise AuthenticationError("邮箱或密码错误")

            # 更新最后登录时间
            user.last_login = datetime.utcnow()
            db.commit()

            logger.info(f"用户认证成功: {user_login.email}")
            return user
            
        except Exception as e:
            logger.error(f"用户认证失败: {str(e)}")
            raise

    def update_user_points(self, db: Session, user_id: int, points_change: int) -> bool:
        """更新用户积分"""
        try:
            user = self.get_user_by_id(db, user_id)
            if not user:
                raise UserNotFoundError("用户不存在")

            new_points = user.points + points_change
            if new_points < 0:
                raise ValidationError("积分不足")

            user.points = new_points
            db.commit()

            logger.info(f"用户积分更新成功: {user_id}, 变化: {points_change}, 当前: {new_points}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"更新用户积分失败: {str(e)}")
            raise

    def deactivate_user(self, db: Session, user_id: int) -> bool:
        """停用用户"""
        try:
            user = self.get_user_by_id(db, user_id)
            if not user:
                raise UserNotFoundError("用户不存在")

            user.is_active = False
            db.commit()

            logger.info(f"用户停用成功: {user_id}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"停用用户失败: {str(e)}")
            raise

    def verify_user(self, db: Session, user_id: int) -> bool:
        """验证用户"""
        try:
            user = self.get_user_by_id(db, user_id)
            if not user:
                raise UserNotFoundError("用户不存在")

            user.is_verified = True
            db.commit()

            logger.info(f"用户验证成功: {user_id}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"验证用户失败: {str(e)}")
            raise


# 创建全局认证服务实例
auth_service = AuthService()
